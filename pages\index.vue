<template>
  <div
    class="desktop carousel-wrapper">
    <el-carousel
      ref="carousel"
      :autoplay="false"
      :initial-index="initialIndex"
      indicator-position="none"
      @change="indexChange">
      <template
        v-for="(item, index) in desktopList">
        <el-carousel-item
          v-if="item.dpCheck !== -1"
          :name="item.id"
          :key="item.id"
          class="carousel-item">
          <div class="carousel-desktop">
            <windows-app-list
              v-if="item.dpCheck === 1"
              @on-submit="saveDesktopConfig(index)"/>
            <windows-widget
              :widget-list="item.config.widgetList"
              @change="saveWidget($event, index)"
              @change-pos="changeWidgetPos($event, index)"
              @change-size="changeWidgetSize($event, index)"
              @delete-widget="deleteWidget($event, index)"/>
          </div>
        </el-carousel-item>
        <el-carousel-item
          v-if="item.dpCheck === -1"
          :key="item.id">
          <windows-nav/>
        </el-carousel-item>
      </template>
    </el-carousel>
  </div>
</template>

<script>
import hmiDialog from '@/components/index/HmiDialog'
import rightMenu from '@/components/index/RightMenu'
import appList from '@/components/index/AppList'
import WindowsAppList from '@/components/index/WindowsAppList'
import { post } from '@/lib/Util'
import {
  deleteDesktop,
  findRootResource,
  getDefaultDesktop,
  getDesktopByUserNo,
  getDesktopConfig,
  getDesktopConfigsByUserNO,
  saveDesktopConfig
} from '@/api/desktop'
import Widget from '@/components/widget/Widget'
import { resourceListNoPage } from '@/api/system'
import WindowsWidget from '@/components/index/WindowsWidget'
import VueGridLayout from 'vue-grid-layout'
import lodash from 'lodash'
import uuid from 'uuid'
import { mapGetters, mapState } from 'vuex'
import WindowsNav from '@/components/index/WindowsNav'

export default {
  name: 'Index',
  components: {
    WindowsNav,
    WindowsWidget,
    Widget,
    WindowsAppList,
    hmiDialog,
    rightMenu,
    appList,
    GridLayout: VueGridLayout.GridLayout,
    GridItem: VueGridLayout.GridItem
  },
  data() {
    return {
      desktopListCache: [],
      initialIndex: 0,
      layout: []
    }
  },
  computed: {
    ...mapState('menu', ['allMenus']),
    ...mapState('desktop', ['activeIndex', 'desktopList'])
  },
  watch: {
    desktopListCache: {
      deep: true,
      handler(val) {
        this.$store.commit('desktop/desktopList', val)
      }
    }
  },
  created() {
    this.initialIndex = this.activeIndex
    this.getResource()
  },
  mounted() {
    this.subscribe()
  },
  destroyed() {
    this.$bus.$off('add-widget')
    this.$bus.$off('add-desktop')
    this.$bus.$off('delete-desktop')
    this.$bus.$off('switch-desktop')
  },
  methods: {
    indexChange(index) {
      this.$store.commit('desktop/activeIndex', index)
    },
    subscribe() {
      // 添加小组件
      this.$bus.$on('add-widget', data => {
        // 在激活页添加小部件，读取设置默认大小
        if (!data) return
        this.desktopListCache[this.activeIndex].config.widgetList.push(data)
        this.saveDesktopConfig(this.activeIndex)
        // this.$store.commit('desktop/dragWidget', null)
      })
      // 添加自定义桌面
      this.$bus.$on('add-desktop', data => {
        console.log(data)
        // 添加桌面数据
        this.desktopListCache.push(data)
        // 激活指定走马灯
        this.$nextTick(() => {
          this.$refs.carousel.setActiveItem(data.id)
        })
      })
      // 删除桌面
      this.$bus.$on('delete-desktop', index => {
        this.desktopListCache.splice(index, 1)
      })
      // 切换桌面
      this.$bus.$on('switch-desktop', index => {
        this.$store.commit('desktop/activeIndex', index)
        this.$refs.carousel.setActiveItem(this.activeIndex)
      })
      // 监听子页面消息
      window.addEventListener('message', this.handleMessage)
    },
    // 添加用户资源
    async getResource() {
      const desktopList = await this.getUserDesktop()

      // 未激活则 激活主桌面 激活顺序 导航页 > 桌面
      const shortcut = desktopList.findIndex(item => item.dpCheck === 1) // 桌面页
      const navIndex = desktopList.findIndex(item => item.dpCheck === -1) // 导航页
      console.log(navIndex)
      this.initialIndex = navIndex === -1 ? shortcut : navIndex
      this.$store.commit('desktop/activeIndex', this.initialIndex)
      this.$refs.carousel.setActiveItem(this.initialIndex)

      this.desktopListCache = desktopList
      const shortcutListDeskTop = this.desktopListCache[shortcut]
      // 如果没有shortcutList，则桌面没有初始化，初始化主页
      if (!shortcutListDeskTop.config.shortcutList.length) {
        const rootResource = await post(findRootResource, {
          userNo: localStorage.getItem('userId'), //传入
          type: 'menu'
        })
        const list = rootResource.data.map(item => {
          item.drop = null
          return item
        })
        this.$store.commit('desktop/shortcutList', list)
        await this.saveDesktopConfig(this.activeIndex)
      } else {
        this.$store.commit(
          'desktop/shortcutList',
          shortcutListDeskTop.config.shortcutList
        )
      }
    },
    async getUserDesktop() {
      // 根据桌面列表和桌面配置合并生成桌面信息
      const [desktop, configs, allMenus] = await Promise.all([
        post(getDesktopByUserNo, {
          userNo: localStorage.getItem('userId')
        }),
        post(getDesktopConfigsByUserNO, {
          userNo: localStorage.getItem('userId')
        }),
        post(resourceListNoPage, {
          userNo: localStorage.getItem('userId')
        })
      ])
      let result = desktop.data.map(item => {
        item.config = {}
        const match = configs.data.find(
          configItem => configItem.desktopID === item.id
        )
        if (match) {
          // console.log(match)
          item.desktopID = match.desktopID
          item.config = JSON.parse(match.config)
          // 根据最新个人权限更新配置信息
          this.updateInfo(item.config, allMenus.data || [])
        } else {
          item.config = {
            widgetList: [],
            shortcutList: []
          }
        }
        return item
      })
      result = lodash.sortBy(result, 'dpCheck')

      // 判断是否有导航页权限
      const matchMenu = this.allMenus.find(item => item.url === '/desktop-nav')
      matchMenu &&
        result.splice(1, 0, {
          id: 'desktop-nav',
          dpCheck: -1,
          name: '导航页'
        })

      return new Promise(resolve => resolve(result))
    },
    // 根据最新个人权限更新桌面配置信息
    updateInfo(config, allMenus) {
      // console.log(config)
      if (config.widgetList && config.widgetList.length) {
        // 倒叙小部件
        for (let i = config.widgetList.length - 1; i >= 0; i--) {
          const index = allMenus.findIndex(
            menu => menu.id === config.widgetList[i].id
          )
          if (index !== -1) {
            // 更新信息
            config.widgetList[i].url = allMenus[index].url
            config.widgetList[i].name = allMenus[index].name
          } else {
            config.widgetList.splice(i, 1)
          }
        }
      }
      if (config.shortcutList && config.shortcutList.length) {
        // 倒叙遍历 快捷方式
        for (let i = config.shortcutList.length - 1; i >= 0; i--) {
          // 更新文件夹
          if (
            config.shortcutList[i].children &&
            config.shortcutList[i].children.length
          ) {
            for (
              let j = config.shortcutList[i].children.length - 1;
              j >= 0;
              j--
            ) {
              const cIndex = allMenus.findIndex(
                menu => menu.id === config.shortcutList[i].children[j].id
              )
              if (cIndex !== -1) {
                // 更新信息
                config.shortcutList[i].children[j].url = allMenus[cIndex].url
                config.shortcutList[i].children[j].deskIcon =
                  allMenus[cIndex].deskIcon
                config.shortcutList[i].children[j].icon = allMenus[cIndex].icon
              } else {
                config.shortcutList[i].children.splice(i, 1)
              }
            }
          } else {
            // 更新快捷方式
            const index = allMenus.findIndex(
              menu => menu.id === config.shortcutList[i].id
            )
            if (index !== -1) {
              // 更新信息
              config.shortcutList[i].url = allMenus[index].url
              config.shortcutList[i].icon = allMenus[index].icon
              config.shortcutList[i].deskIcon = allMenus[index].deskIcon
              config.shortcutList[i].port = allMenus[index].port
              config.shortcutList[i].ip = allMenus[index].ip
            } else {
              // 非文件夹需要删除
              config.shortcutList.splice(i, 1)
            }
          }
        }
      }
    },
    // 保存桌面配置
    async saveDesktopConfig(index) {
      if (this.desktopListCache[index].dpCheck === 1) {
        // 如果是主页面
        this.desktopListCache[
          index
        ].config.shortcutList = this.$store.state.desktop.shortcutList
      }
      const data = await post(saveDesktopConfig, {
        userNo: localStorage.getItem('userId'),
        desktopID: this.desktopListCache[index].id,
        config: this.desktopListCache[index].config
      })
      if (data.success) return console.log('桌面保存成功')
      console.warn('桌面保存失败')
    },
    saveWidget(widgets, index) {
      this.saveDesktopConfig(index)
    },
    deleteWidget(cIndex, index) {
      this.desktopListCache[index].config.widgetList.splice(cIndex, 1)
      this.saveDesktopConfig(index)
    },
    /**
     * 改变物件size
     * @param e
     * @param index
     */
    changeWidgetSize(e, index) {
      const [cIndex, h, w] = e
      const temp = this.desktopListCache[index].config.widgetList[cIndex]
      temp.w = w
      temp.h = h
      this.desktopListCache[index].config.widgetList.splice(cIndex, 1, temp)
      this.saveDesktopConfig(index)
    },
    /**
     * 改变物件位置
     * @param e
     * @param index
     */
    changeWidgetPos(e, index) {
      const [cIndex, x, y] = e
      const temp = this.desktopListCache[index].config.widgetList[cIndex]
      temp.x = x
      temp.y = y
      this.desktopListCache[index].config.widgetList.splice(cIndex, 1, temp)
      this.saveDesktopConfig(index)
    },
    handleMessage(e) {
      console.log('消息接收', e)
      const { data } = e
      switch (data.type) {
        case 'link':
          // 完整路径跳转
          this.$bus.$emit('open-iframe', {
            url: data.url,
            ip: data.url,
            type: 'custom',
            name: data.title
          })
          break
        case 'menu':
          // 根据页面url serviceName 跳转
          const match = this.allMenus.find(
            item =>
              item.serviceName === data.serviceName && item.url === data.url
          )
          if (match) {
            this.$bus.$emit('open-iframe', match)
          } else {
            this.$message.warning('暂无访问此页面的权限，请联系管理员！')
          }
          break
        default:
          break
      }
    }
  }
}
</script>

<style lang="less">
.app-list .icon-svg {
  font-size: 70px !important;
}
#iframeContain {
  width: 100%;
  height: 900px;
}

.dialog-content {
  background-color: #fff;
  height: 100%;
  width: 100%;
}
.desktop {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.app-wrapper {
  position: relative;
  height: 100%;
  width: 50%;
  float: left;
  .app-list {
    height: 100%;
    //border: 1px solid red;
    padding: 8px 16px;
    .row {
      height: 100%;
    }
  }
}

.news-list {
  width: 50%;
  height: 100%;
  float: left;
  padding: 25px;
  //border: 1px solid red;
}

.conip {
  //display: inline-grid;
  display: grid;
  //width: 85px;
  height: 200px;
  background-color: #ffffff00;
  //margin: 10px 0 10px 0;
  margin-bottom: 5px;
  padding: 0;
  text-align: center;
  font-size: 0;
}

.conip::after {
  display: none;
}

.conip.selected::after {
  position: absolute;
  display: inline-grid;
  width: 10px;
  height: 10px;
  margin-left: 0px;
  border-radius: 14px;
  background-color: #0000ff66;
  content: ' ';

  overflow: hidden;
  pointer-events: none;
  // border: solid transparent 1 px;
  transition: transform 0.3s;
}

.selected .icon {
  // background-color: #c8c7f9;
  // border: solid blue 1 px !important;
}

.file {
  background: #ffffffff;
}

.icon {
  display: inline-grid;
  width: 95px;
  height: 95px;
  margin-left: 0px;
  border-radius: 14px;

  overflow: hidden;
  // border: solid transparent 1 px;
  transition: transform 0.3s;

  //box-shadow: -2px 4px 19px rgba(0, 0, 0, 0.09);
}

.ihover {
  transform: scale(1.065);
  transition: transform 0.25s;
}

.icon_h.conip.selected::after,
.icon_h .icon {
  border-radius: 4px;
  transform: scale(1.8);
  // z-index: 10000;
  // box-shadow: 0 0 px 8 px rgba(0, 0, 0, 0);
}

.icon img {
  font-size: 0;
  position: absolute;
  display: inline-grid;
  width: 95px;
  height: 95px;
  border-radius: 9px;
  pointer-events: none;
  box-shadow: 0 0px 8px rgba(0, 0, 0, 0.2);
}

.icon_0 {
  left: 0;
}

.icon_1 {
  right: 0;
}

.icon_2 {
  bottom: 0;
}

.icon_3 {
  bottom: 0;
  right: 0;
}

.app-list span {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 12px;
  //color: #fff;
  color: #fff;
  font-weight: normal;
}

#folderName {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 12px;
  color: #333;
  font-weight: normal;
  text-align: center;
  padding: 4px;
  border: none;
  outline: none;
}

.toptop {
  position: relative;
  width: 100%;
  height: 100%;
  margin-left: 0px;
  visibility: hidden;
  //visibility: hidden;
}

.app-list .col {
  padding-right: 0;
  padding-bottom: 0px;
  margin-right: 0px;
  text-align: left;
  display: inline-flex;
  height: 100%;
  flex-direction: column;
  flex-wrap: wrap;
}

#drwppp {
  position: absolute;
  top: 0;
  //width: 50%;
  height: 100px;
  margin-right: 0px;
  text-align: left;

  margin-left: 0px;
  border-radius: 12px;
  background: #ffffffff;
  border: solid #ccc 1px;
  box-shadow: 0 0px 8px rgba(0, 0, 0, 0.2);
  opacity: 1;
  overflow: scroll;
  transition: transform 0.3s;
}

#drwpp {
  overflow: scroll;
  position: relative;
  grid-auto-flow: column;
  display: grid;
}

.d_hide {
  transform: scale(0);
  // visibility: hidden;
}

#drwpp {
}

#APP ::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

#drawer {
  //position: absolute;
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  //z-index: 999;
  // background-color: #cc85d9;
}

.drawer1 {
}

.blue-background-class {
  visibility: hidden;
  // background-color: #c8ebfb;
}

.highlight {
  background-color: #b7f8c7;
}

.clear {
  display: block;
  height: 0px;
  line-height: 0px;
  font-size: 0px;
  clear: both;
}

.carousel-wrapper {
  height: 100%;
  width: 100%;
  .el-carousel {
    height: 100%;
    .el-carousel__container {
      height: 100%;
      //block-size: inherit;
      .el-carousel__arrow {
        width: 40px;
        height: 40px;
        //background-color: transparent;
        i {
          font-size: 25px;
        }
      }
      .el-carousel__arrow--right {
        right: 10px;
      }
      .el-carousel__arrow--left {
        left: 10px;
      }
      .el-carousel__item {
        .img-box {
          height: 100%;
          background-size: contain;
          max-width: 800px;
          margin: 0 auto;
        }
      }
    }
  }
  .carousel-item {
    height: 100%;
    width: 100%;
    .carousel-desktop {
      padding-top: 48px;
      height: 100%;
    }
  }
  .carousel-panel {
    margin: 20px;
  }
}
</style>
