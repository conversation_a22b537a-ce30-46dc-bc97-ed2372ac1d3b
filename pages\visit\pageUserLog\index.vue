<template>
  <div class="page-content">
    <div class="page-operate">
      <div class="search-wrapper">
        <el-form
          ref="searchForm"
          :label-width="'80px'"
          :model="searchForm"
          size="small"
          inline
          @keyup.enter.native="handleSearch(true)"
        >
          <el-form-item
            label=""
            prop="userNo"
          >
            <el-input
              v-model="searchForm.userNo"
              clearable
              size="small"
              placeholder="请输入员工编号或姓名"
              style="width: 200px"
              suffix-icon="el-icon-search"
              type="text"
            />
          </el-form-item>
          <el-form-item
            label=""
            prop="userNo"
          >
            <select-org
              v-model="searchForm.orgCode"
              :multiple="false"/>
          </el-form-item>
          <el-form-item
            label="开始时间"
            prop="startTime"
          >
            <el-date-picker
              v-model="searchForm.dateRange"
              :value-format="'yyyy-MM-dd'"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @input="$forceUpdate()"/>
          </el-form-item>
          <el-form-item
            prop="leaderType"
          >
            <el-radio
              v-command="'/visit/pageUserLog/seniorLeaderView'"
              v-model="searchForm.leaderType"
              :label="1"
              @input="handleSearch(true)">高层</el-radio>
            <el-radio
              v-command="'/visit/pageUserLog/middleLeaderView'"
              v-model="searchForm.leaderType"
              :label="2"
              @input="handleSearch(true)">中层</el-radio>
            <el-radio
              v-model="searchForm.leaderType"
              :label="3"
              @input="handleSearch(true)">E层</el-radio>
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="searchForm.leaderType"
              type="text"
              @click="searchForm.leaderType = '';handleSearch(true)">取消选择</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-button
          icon="ios-search"
          size="small"
          type="primary"
          @click="handleSearch"
        >搜索
        </el-button>
        <el-button
          icon="ios-search"
          size="small"
          type="primary"
          @click="handleExport"
        >导出
        </el-button>
        <el-button
          icon="ios-search"
          size="small"
          type="primary"
          @click="importDateVisible = true"
        >访问统计
        </el-button>
        <el-button
          icon="ios-search"
          size="small"
          type="primary"
          @click="handleViewCharts">
          应用访问统计表
        </el-button>
        <el-button
          v-command="'/visit/pageUserLog/userExport'"
          icon="ios-search"
          size="small"
          type="primary"
          @click="userExport">
          应访问用户统计
        </el-button>
      </div>
    </div>
    <div class="page-card shadow-light">
      <el-table
        v-loading="loading"
        :data="tableData"
        :size="size"
        class="custom-table"
        border
        style="width: 100%"
      >
        <el-table-column
          label="序号"
          type="index"
          width="100"
        />
        <el-table-column
          label="员工编号"
          prop="userNo"
          width="100"
        />
        <el-table-column
          label="姓名"
          prop="userName"
          width="120"
        />
        <el-table-column
          label="部门"
          prop="orgAllName"
        />
        <el-table-column
          label="访问次数"
          prop="percent"
          width="280"
        >
          <template v-slot="{ row }">
            <div
              :class="getStatus(row.percent)"
              class="progress-box">
              <el-progress
                :stroke-width="18"
                :text-inside="true"
                :percentage="row.percent || 0"
                :status="getStatus(row.percent)"
                style="display: inline-block; width: 180px"/>
              <span class="progress-num">
                {{ row.accessNum }}次
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          prop="percent"
          width="120"
        >
          <template v-slot="{ row }">
            <el-button
              v-command="'/visit/pageUserLog/view'"
              type="text"
              @click="handleView(row)"
            >访问记录
            </el-button>
            <el-button
              type="text"
              @click="recordsExport(row)"
            >导出
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-row
        align="middle"
        class="table-pagination"
        justify="end"
        type="flex"
      >
        <el-pagination
          :current-page="page.pageIndex"
          :page-size="page.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :total="page.total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-row>
    </div>
    <list
      ref="modalView"
      :service-list="serviceList"
      :login-time-start="searchForm.loginTimeStart"
      :login-time-end="searchForm.loginTimeEnd"/>
    <!--导入日期-->
    <el-dialog
      :visible.sync="importDateVisible"
      :width="'600px'"
      :close-on-click-modal="false"
      :append-to-body="false"
      class="screen-dialog"
      title="领导与单位访问统计">
      <template v-slot:title>
        <div class="custom-dialog-title">
          领导与单位访问统计
        </div>
      </template>
      <el-form
        label-width="120px"
        class="demo-form-inline">
        <el-form-item label="导出日期">
          <el-date-picker
            v-model="importDate"
            :clearable="false"
            :value-format="'yyyy-MM-dd'"
            type="daterange"/>
        </el-form-item>
        <div class="text-center">
          <el-button
            :loading="loading"
            type="primary"
            @click="handleStatisticsExport()">确定</el-button>
        </div>
      </el-form>
    </el-dialog>
    <!--应用访问统计表-->
    <visitCharts
      ref="modalDetailForm" />
  </div>

</template>

<script>
import listMixins from '@/mixins/ListMixins'
import {
  exportLeaderAndOrgExcel,
  exportUserDetExcel,
  findBasicDataConfigByType,
  findUserPageAccessList,
  pageLogExportExcel,
  exportShouldUserAccessExcel
} from '@/api/system'
import SelectOrg from '@/components/SelectOrg'
import { post } from '@/lib/Util'
import List from '@/pages/visit/pageUserLog/component/list'
import visitCharts from './component/visitCharts.vue'

export default {
  name: 'visit-pageUserLog',
  components: { visitCharts, List, SelectOrg },
  layout: 'menuLayout',
  mixins: [listMixins],
  data: () => {
    return {
      importDateVisible: false,
      importDate: null,
      searchForm: {
        leaderType: null,
        orgCode: null
      },
      serviceList: [],
      visibleEdit: false,
      visibleDistribute: false,
      url: {
        list: findUserPageAccessList //分页接口地址
      },
      editUserId: null,
      maxNum: 20
    }
  },
  created() {
    this.searchForm.orgCode = JSON.parse(
      localStorage.getItem('userDetail') || '{}'
    ).orgCode
    this.findBasicDataConfigByType()
    this.importDate = [
      this.$moment()
        .startOf('month')
        .format('yyyy-MM-DD'),
      this.$moment().format('yyyy-MM-DD')
    ]
  },
  methods: {
    //
    afterHandleSearch() {
      this.tableData = this.tableData.map(item => {
        item.percent = Number(((item.accessNum / this.maxNum) * 100).toFixed(1))
        item.percent = item.percent > 100 ? 100 : item.percent
        return item
      })
    },
    beforeHandleSearch() {
      if (!this.searchForm.dateRange) {
        this.searchForm.dateRange = [
          this.$moment().format('yyyy-MM-DD'),
          this.$moment().format('yyyy-MM-DD')
        ]
      }
      this.searchForm.loginTimeStart = this.searchForm.dateRange[0]
      this.searchForm.loginTimeEnd = this.searchForm.dateRange[1]
    },
    getStatus(percent) {
      if (percent >= 80) {
        return 'success'
      } else if (percent >= 50) {
        return 'warning'
      } else {
        return 'exception'
      }
    },
    handleView: function(row) {
      this.$refs.modalView.userNo = row.userNo
      this.$refs.modalView.visible = true
      this.$refs.modalView.handleSearch(true)
    },
    handleViewCharts: function() {
      this.$refs.modalDetailForm.visible = true
      this.getData()
    },
    //行导出
    recordsExport(row) {
      this.loading = true
      console.log('userNo', row)
      post(
        pageLogExportExcel,
        Object.assign({
          userNo: row.userNo,
          loginTimeStart: this.searchForm.dateRange[0],
          loginTimeEnd: this.searchForm.dateRange[1]
        }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          '访问记录(' + row.userNo + row.userName + ').xls'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    },
    //导出
    async handleExport() {
      this.beforeHandleSearch()
      post(
        exportUserDetExcel,
        Object.assign({}, this.searchForm, { pageSize: 10000 }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          '用户访问记录(' +
            this.searchForm.loginTimeStart +
            '-' +
            this.searchForm.loginTimeEnd +
            ').xls'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.visible = false
        this.loading = false
      })
    },
    //应访问用户统计
    async userExport() {
      this.beforeHandleSearch()
      post(
        exportShouldUserAccessExcel,
        Object.assign({}, this.searchForm, {
          pageSize: 10000,
          handleUserFla: false
        }),
        false,
        {
          responseType: 'blob'
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          '应访问用户统计(' +
            this.searchForm.loginTimeStart +
            '-' +
            this.searchForm.loginTimeEnd +
            ').xls'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.visible = false
        this.loading = false
      })
    },
    async handleStatisticsExport() {
      this.loading = true
      post(
        exportLeaderAndOrgExcel,
        Object.assign(
          {},
          {
            loginTimeStart: this.importDate[0],
            loginTimeEnd: this.importDate[1]
          },
          { pageSize: 10000 }
        ),
        false,
        {
          responseType: 'blob',
          timeout: 1800000 // 30分钟超时时间（30 * 60 * 1000 = 1800000毫秒）
        }
      ).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          '领导与单位访问统计(' +
            this.importDate[0] +
            '-' +
            this.importDate[1] +
            ').xls'
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        // 关闭弹窗
        this.importDateVisible = false
        this.loading = false
      })
    },
    async findBasicDataConfigByType() {
      // var data = JSON.parse(info)
      const data = await post(
        findBasicDataConfigByType,
        {
          type: 'serviceInfo'
        },
        'no'
      )
      let dataContent = []
      if (data.content) {
        dataContent = JSON.parse(data.content)
        data.content = dataContent
        if (data.flag === 'json') {
          var dataTypeContent = []
          dataContent.map(function(item, index) {
            var itemType = typeof item === 'string' ? JSON.parse(item) : item
            dataTypeContent.push(itemType)
          })
          data.content = dataTypeContent
        }
      }
      this.serviceList = data.content
      return Promise.resolve(true)
    }
  }
}
</script>

<style
  lang="less"
  scoped
>
.table-pagination {
  margin-top: 20px;
}

.tree-tit {
  margin-bottom: 15px;
  font-size: 16px;
  line-height: 1.5;
}

.tree-wrapper {
  height: 75vh;
  overflow: auto;
  padding: 5px;
  border: 1px solid #eee;
}
.progress-box {
  display: flex;
  font-size: 18px;
  font-weight: bold;
  .progress-num {
    flex: 1;
    margin-left: 5px;
    text-align: right;
  }
  &.exception {
    color: #ff2855;
  }
  &.warning {
    color: #ffb243;
  }
  &.success {
    color: #19be6b;
  }
}
</style>
